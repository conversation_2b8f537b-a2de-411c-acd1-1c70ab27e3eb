//
//  ReminderModel.swift
//  RemindersApp
//
//  Created by User on 2025-07-25.
//

import Foundation
import SwiftUI

// MARK: - Reminder Model
struct Reminder: Identifiable, Codable, Hashable {
    var id: UUID
    var text: String
    var isCompleted: Bool
    var dueDate: Date?
    var category: ReminderCategory
    var priority: ReminderPriority
    var createdAt: Date
    var completedAt: Date?
    var notes: String

    init(text: String,
         dueDate: Date? = nil,
         category: ReminderCategory = .personal,
         priority: ReminderPriority = .medium,
         notes: String = "") {
        self.id = UUID()
        self.text = text
        self.isCompleted = false
        self.dueDate = dueDate
        self.category = category
        self.priority = priority
        self.notes = notes
        self.createdAt = Date()
        self.completedAt = nil
    }
    
    // Computed properties
    var isOverdue: Bool {
        guard let dueDate = dueDate, !isCompleted else { return false }
        return dueDate < Date()
    }
    
    var dueDateString: String? {
        guard let dueDate = dueDate else { return nil }

        let calendar = Calendar.current

        if calendar.isDateInToday(dueDate) {
            return "Today"
        } else if calendar.isDateInTomorrow(dueDate) {
            return "Tomorrow"
        } else if calendar.isDateInYesterday(dueDate) {
            return "Yesterday"
        } else {
            let formatter = DateFormatter()
            formatter.dateStyle = .medium
            return formatter.string(from: dueDate)
        }
    }
    
    mutating func toggleCompletion() {
        isCompleted.toggle()
        completedAt = isCompleted ? Date() : nil
    }
}

// MARK: - Reminder Category
enum ReminderCategory: String, CaseIterable, Codable {
    case personal = "Personal"
    case work = "Work"
    case shopping = "Shopping"
    case health = "Health"
    
    var color: Color {
        switch self {
        case .personal:
            return .blue
        case .work:
            return .orange
        case .shopping:
            return .green
        case .health:
            return .red
        }
    }
    
    var icon: String {
        switch self {
        case .personal:
            return "person.fill"
        case .work:
            return "briefcase.fill"
        case .shopping:
            return "cart.fill"
        case .health:
            return "heart.fill"
        }
    }
}

// MARK: - Reminder Priority
enum ReminderPriority: String, CaseIterable, Codable {
    case low = "Low"
    case medium = "Medium"
    case high = "High"
    
    var color: Color {
        switch self {
        case .low:
            return .gray
        case .medium:
            return .orange
        case .high:
            return .red
        }
    }
    
    var sortOrder: Int {
        switch self {
        case .high:
            return 3
        case .medium:
            return 2
        case .low:
            return 1
        }
    }
}

// MARK: - Sort Options
enum SortOption: String, CaseIterable {
    case createdDate = "Created Date"
    case dueDate = "Due Date"
    case priority = "Priority"
    case category = "Category"
    case alphabetical = "Alphabetical"
    
    var systemImage: String {
        switch self {
        case .createdDate:
            return "clock"
        case .dueDate:
            return "calendar"
        case .priority:
            return "exclamationmark.triangle"
        case .category:
            return "folder"
        case .alphabetical:
            return "textformat.abc"
        }
    }
}

// MARK: - Filter Options
enum FilterOption: String, CaseIterable {
    case all = "All"
    case pending = "Pending"
    case completed = "Completed"
    case overdue = "Overdue"
    case today = "Today"
    case upcoming = "Upcoming"
    
    var systemImage: String {
        switch self {
        case .all:
            return "list.bullet"
        case .pending:
            return "circle"
        case .completed:
            return "checkmark.circle.fill"
        case .overdue:
            return "exclamationmark.triangle.fill"
        case .today:
            return "calendar.badge.clock"
        case .upcoming:
            return "calendar.badge.plus"
        }
    }
    
    var color: Color {
        switch self {
        case .all:
            return .primary
        case .pending:
            return .blue
        case .completed:
            return .green
        case .overdue:
            return .red
        case .today:
            return .orange
        case .upcoming:
            return .purple
        }
    }
}

// MARK: - Sample Data
extension Reminder {
    static let sampleData: [Reminder] = [
        Reminder(
            text: "Buy groceries for the week",
            dueDate: Calendar.current.date(byAdding: .day, value: 1, to: Date()),
            category: .shopping,
            priority: .medium
        ),
        Reminder(
            text: "Finish quarterly report",
            dueDate: Calendar.current.date(byAdding: .day, value: 3, to: Date()),
            category: .work,
            priority: .high
        ),
        Reminder(
            text: "Schedule dentist appointment",
            dueDate: Calendar.current.date(byAdding: .day, value: 7, to: Date()),
            category: .health,
            priority: .medium
        ),
        Reminder(
            text: "Plan weekend trip",
            dueDate: Calendar.current.date(byAdding: .day, value: 14, to: Date()),
            category: .personal,
            priority: .low
        ),
        {
            var reminder = Reminder(
                text: "Review team performance",
                category: .work,
                priority: .high
            )
            reminder.isCompleted = true
            reminder.completedAt = Calendar.current.date(byAdding: .day, value: -2, to: Date())
            return reminder
        }()
    ]
}
