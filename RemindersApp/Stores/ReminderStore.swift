//
//  ReminderStore.swift
//  RemindersApp
//
//  Created by User on 2025-07-25.
//

import Foundation
import SwiftUI
import UserNotifications

@MainActor
class ReminderStore: ObservableObject {
    @Published var reminders: [Reminder] = []
    @Published var searchText: String = ""
    @Published var selectedCategory: ReminderCategory?
    @Published var selectedFilter: FilterOption = .all
    @Published var sortOption: SortOption = .createdDate
    @Published var isAscending: Bool = false
    
    private let userDefaults = UserDefaults.standard
    private let remindersKey = "SavedReminders"
    
    init() {
        loadReminders()
        requestNotificationPermission()
    }
    
    // MARK: - Computed Properties
    var filteredReminders: [Reminder] {
        var filtered = reminders
        
        // Apply search filter
        if !searchText.isEmpty {
            filtered = filtered.filter { reminder in
                reminder.text.localizedCaseInsensitiveContains(searchText) ||
                reminder.notes.localizedCaseInsensitiveContains(searchText)
            }
        }
        
        // Apply category filter
        if let selectedCategory = selectedCategory {
            filtered = filtered.filter { $0.category == selectedCategory }
        }
        
        // Apply status filter
        switch selectedFilter {
        case .all:
            break
        case .pending:
            filtered = filtered.filter { !$0.isCompleted }
        case .completed:
            filtered = filtered.filter { $0.isCompleted }
        case .overdue:
            filtered = filtered.filter { $0.isOverdue }
        case .today:
            filtered = filtered.filter { reminder in
                guard let dueDate = reminder.dueDate else { return false }
                return Calendar.current.isDateInToday(dueDate)
            }
        case .upcoming:
            filtered = filtered.filter { reminder in
                guard let dueDate = reminder.dueDate, !reminder.isCompleted else { return false }
                return dueDate > Date()
            }
        }
        
        // Apply sorting
        filtered.sort { first, second in
            let result: Bool
            
            switch sortOption {
            case .createdDate:
                result = first.createdAt < second.createdAt
            case .dueDate:
                if first.dueDate == nil && second.dueDate == nil {
                    result = first.createdAt < second.createdAt
                } else if first.dueDate == nil {
                    result = false
                } else if second.dueDate == nil {
                    result = true
                } else {
                    result = first.dueDate! < second.dueDate!
                }
            case .priority:
                result = first.priority.sortOrder > second.priority.sortOrder
            case .category:
                result = first.category.rawValue < second.category.rawValue
            case .alphabetical:
                result = first.text.localizedCaseInsensitiveCompare(second.text) == .orderedAscending
            }
            
            return isAscending ? result : !result
        }
        
        return filtered
    }
    
    var stats: (total: Int, completed: Int, pending: Int, overdue: Int) {
        let total = reminders.count
        let completed = reminders.filter { $0.isCompleted }.count
        let pending = reminders.filter { !$0.isCompleted }.count
        let overdue = reminders.filter { $0.isOverdue }.count
        
        return (total: total, completed: completed, pending: pending, overdue: overdue)
    }
    
    // MARK: - CRUD Operations
    func addReminder(_ reminder: Reminder) {
        reminders.append(reminder)
        saveReminders()
        scheduleNotification(for: reminder)
    }
    
    func updateReminder(_ reminder: Reminder) {
        if let index = reminders.firstIndex(where: { $0.id == reminder.id }) {
            reminders[index] = reminder
            saveReminders()
            scheduleNotification(for: reminder)
        }
    }
    
    func deleteReminder(_ reminder: Reminder) {
        reminders.removeAll { $0.id == reminder.id }
        saveReminders()
        cancelNotification(for: reminder)
    }
    
    func deleteReminders(at offsets: IndexSet) {
        let remindersToDelete = offsets.map { filteredReminders[$0] }
        for reminder in remindersToDelete {
            deleteReminder(reminder)
        }
    }
    
    func toggleCompletion(for reminder: Reminder) {
        if let index = reminders.firstIndex(where: { $0.id == reminder.id }) {
            reminders[index].toggleCompletion()
            saveReminders()
            
            if reminders[index].isCompleted {
                cancelNotification(for: reminder)
            } else {
                scheduleNotification(for: reminders[index])
            }
        }
    }
    
    func clearCompleted() {
        let completedReminders = reminders.filter { $0.isCompleted }
        for reminder in completedReminders {
            cancelNotification(for: reminder)
        }
        reminders.removeAll { $0.isCompleted }
        saveReminders()
    }
    
    // MARK: - Persistence
    private func saveReminders() {
        do {
            let data = try JSONEncoder().encode(reminders)
            userDefaults.set(data, forKey: remindersKey)
        } catch {
            print("Failed to save reminders: \(error)")
        }
    }
    
    private func loadReminders() {
        guard let data = userDefaults.data(forKey: remindersKey) else {
            // Load sample data for first launch
            reminders = Reminder.sampleData
            saveReminders()
            return
        }
        
        do {
            reminders = try JSONDecoder().decode([Reminder].self, from: data)
        } catch {
            print("Failed to load reminders: \(error)")
            reminders = []
        }
    }
    
    // MARK: - Import/Export
    func exportReminders() -> Data? {
        do {
            return try JSONEncoder().encode(reminders)
        } catch {
            print("Failed to export reminders: \(error)")
            return nil
        }
    }
    
    func importReminders(from data: Data) -> Bool {
        do {
            let importedReminders = try JSONDecoder().decode([Reminder].self, from: data)
            reminders = importedReminders
            saveReminders()
            
            // Schedule notifications for imported reminders
            for reminder in reminders {
                scheduleNotification(for: reminder)
            }
            
            return true
        } catch {
            print("Failed to import reminders: \(error)")
            return false
        }
    }
    
    // MARK: - Notifications
    private func requestNotificationPermission() {
        UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .badge, .sound]) { granted, error in
            if let error = error {
                print("Notification permission error: \(error)")
            }
        }
    }
    
    private func scheduleNotification(for reminder: Reminder) {
        guard let dueDate = reminder.dueDate, !reminder.isCompleted, dueDate > Date() else { return }
        
        let content = UNMutableNotificationContent()
        content.title = "Reminder"
        content.body = reminder.text
        content.sound = .default
        content.categoryIdentifier = "REMINDER_CATEGORY"
        
        let triggerDate = Calendar.current.dateComponents([.year, .month, .day, .hour, .minute], from: dueDate)
        let trigger = UNCalendarNotificationTrigger(dateMatching: triggerDate, repeats: false)
        
        let request = UNNotificationRequest(identifier: reminder.id.uuidString, content: content, trigger: trigger)
        
        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("Failed to schedule notification: \(error)")
            }
        }
    }
    
    private func cancelNotification(for reminder: Reminder) {
        UNUserNotificationCenter.current().removePendingNotificationRequests(withIdentifiers: [reminder.id.uuidString])
    }
    
    // MARK: - Search and Filter
    func clearFilters() {
        searchText = ""
        selectedCategory = nil
        selectedFilter = .all
    }
    
    func setSortOption(_ option: SortOption, ascending: Bool = false) {
        sortOption = option
        isAscending = ascending
    }
}
