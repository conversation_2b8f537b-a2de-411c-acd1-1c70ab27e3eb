//
//  ContentView.swift
//  RemindersApp
//
//  Created by User on 2025-07-25.
//

import SwiftUI

struct ContentView: View {
    @EnvironmentObject var reminderStore: ReminderStore
    @State private var selectedReminder: Reminder?
    @State private var showingAddReminder = false
    @State private var showingImportExport = false
    @State private var columnVisibility = NavigationSplitViewVisibility.all
    
    var body: some View {
        NavigationSplitView(columnVisibility: $columnVisibility) {
            SidebarView(selectedReminder: $selectedReminder)
                .navigationSplitViewColumnWidth(min: 200, ideal: 250, max: 300)
        } content: {
            ReminderListView(selectedReminder: $selectedReminder)
                .navigationSplitViewColumnWidth(min: 300, ideal: 400, max: 500)
        } detail: {
            if let selectedReminder = selectedReminder {
                ReminderDetailView(reminder: selectedReminder)
                    .navigationSplitViewColumnWidth(min: 300, ideal: 400)
                    .environmentObject(reminderStore)
            } else {
                ReminderDetailPlaceholder()
            }
        }
        .focusedSceneValue(\.reminderStore, reminderStore)
        .toolbar {
            ToolbarItemGroup(placement: .primaryAction) {
                Button(action: { showingAddReminder = true }) {
                    Image(systemName: "plus")
                }
                .help("Add new reminder")
                
                Button(action: { showingImportExport = true }) {
                    Image(systemName: "square.and.arrow.up")
                }
                .help("Import/Export")
                
                Menu {
                    ForEach(SortOption.allCases, id: \.rawValue) { option in
                        Button(action: {
                            reminderStore.setSortOption(option, ascending: reminderStore.isAscending)
                        }) {
                            Label(option.rawValue, systemImage: option.systemImage)
                        }
                    }
                    
                    Divider()
                    
                    Button(action: {
                        reminderStore.isAscending.toggle()
                    }) {
                        Label(
                            reminderStore.isAscending ? "Sort Descending" : "Sort Ascending",
                            systemImage: reminderStore.isAscending ? "arrow.down" : "arrow.up"
                        )
                    }
                } label: {
                    Image(systemName: "arrow.up.arrow.down")
                }
                .help("Sort options")
            }
        }
        .sheet(isPresented: $showingAddReminder) {
            AddReminderView()
                .environmentObject(reminderStore)
        }
        .sheet(isPresented: $showingImportExport) {
            ImportExportView()
                .environmentObject(reminderStore)
        }
    }
}

struct ReminderListView: View {
    @EnvironmentObject var reminderStore: ReminderStore
    @Binding var selectedReminder: Reminder?
    
    var body: some View {
        VStack(spacing: 0) {
            // Search and Filter Bar
            VStack(spacing: 12) {
                SearchBar()
                FilterBar()
            }
            .padding()
            .background(Color(NSColor.controlBackgroundColor))
            
            // Reminders List
            if reminderStore.filteredReminders.isEmpty {
                EmptyStateView()
            } else {
                List(reminderStore.filteredReminders, selection: $selectedReminder) { reminder in
                    ReminderRowView(reminder: reminder)
                        .environmentObject(reminderStore)
                        .tag(reminder)
                }
                .listStyle(.inset)
            }
            
            // Stats Footer
            StatsView()
                .padding()
                .background(Color(NSColor.controlBackgroundColor))
        }
        .navigationTitle("Reminders")
    }
}

struct SearchBar: View {
    @EnvironmentObject var reminderStore: ReminderStore
    
    var body: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.secondary)
            
            TextField("Search reminders...", text: $reminderStore.searchText)
                .textFieldStyle(.roundedBorder)
            
            if !reminderStore.searchText.isEmpty {
                Button(action: { reminderStore.searchText = "" }) {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(.secondary)
                }
                .buttonStyle(.plain)
            }
        }
    }
}

struct FilterBar: View {
    @EnvironmentObject var reminderStore: ReminderStore
    
    var body: some View {
        HStack {
            // Filter by status
            Picker("Filter", selection: $reminderStore.selectedFilter) {
                ForEach(FilterOption.allCases, id: \.rawValue) { filter in
                    Label(filter.rawValue, systemImage: filter.systemImage)
                        .tag(filter)
                }
            }
            .pickerStyle(.menu)
            .frame(maxWidth: 120)
            
            // Filter by category
            Picker("Category", selection: $reminderStore.selectedCategory) {
                Text("All Categories").tag(nil as ReminderCategory?)
                ForEach(ReminderCategory.allCases, id: \.rawValue) { category in
                    Label(category.rawValue, systemImage: category.icon)
                        .tag(category as ReminderCategory?)
                }
            }
            .pickerStyle(.menu)
            .frame(maxWidth: 140)
            
            Spacer()
            
            if reminderStore.searchText.isEmpty && 
               reminderStore.selectedCategory == nil && 
               reminderStore.selectedFilter == .all {
                // No active filters
            } else {
                Button("Clear Filters") {
                    reminderStore.clearFilters()
                }
                .buttonStyle(.bordered)
            }
        }
    }
}

struct EmptyStateView: View {
    @EnvironmentObject var reminderStore: ReminderStore
    
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "checklist")
                .font(.system(size: 64))
                .foregroundColor(.secondary)
            
            if reminderStore.reminders.isEmpty {
                Text("No reminders yet")
                    .font(.title2)
                    .fontWeight(.medium)
                
                Text("Create your first reminder to get started")
                    .foregroundColor(.secondary)
            } else {
                Text("No matching reminders")
                    .font(.title2)
                    .fontWeight(.medium)
                
                Text("Try adjusting your search or filters")
                    .foregroundColor(.secondary)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

struct StatsView: View {
    @EnvironmentObject var reminderStore: ReminderStore
    
    var body: some View {
        let stats = reminderStore.stats
        
        HStack {
            StatItem(title: "Total", value: stats.total, color: .primary)
            StatItem(title: "Pending", value: stats.pending, color: .blue)
            StatItem(title: "Completed", value: stats.completed, color: .green)
            if stats.overdue > 0 {
                StatItem(title: "Overdue", value: stats.overdue, color: .red)
            }
        }
    }
}

struct StatItem: View {
    let title: String
    let value: Int
    let color: Color
    
    var body: some View {
        VStack(spacing: 2) {
            Text("\(value)")
                .font(.title3)
                .fontWeight(.semibold)
                .foregroundColor(color)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
    }
}

struct ReminderDetailPlaceholder: View {
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "sidebar.left")
                .font(.system(size: 64))
                .foregroundColor(.secondary)
            
            Text("Select a reminder")
                .font(.title2)
                .fontWeight(.medium)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(NSColor.textBackgroundColor))
    }
}

struct ReminderDetailView: View {
    @EnvironmentObject var reminderStore: ReminderStore
    let reminder: Reminder
    @State private var showingEditSheet = false

    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 20) {
                // Header
                VStack(alignment: .leading, spacing: 12) {
                    HStack {
                        Button(action: {
                            reminderStore.toggleCompletion(for: reminder)
                        }) {
                            Image(systemName: reminder.isCompleted ? "checkmark.circle.fill" : "circle")
                                .font(.title)
                                .foregroundColor(reminder.isCompleted ? .green : .secondary)
                        }
                        .buttonStyle(.plain)

                        VStack(alignment: .leading, spacing: 4) {
                            Text(reminder.text)
                                .font(.title2)
                                .fontWeight(.semibold)
                                .strikethrough(reminder.isCompleted)
                                .foregroundColor(reminder.isCompleted ? .secondary : .primary)

                            Text("Created \(reminder.createdAt, style: .relative) ago")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }

                        Spacer()

                        Button("Edit") {
                            showingEditSheet = true
                        }
                        .buttonStyle(.bordered)
                    }
                }

                // Metadata
                VStack(alignment: .leading, spacing: 16) {
                    DetailRow(title: "Category", content: {
                        HStack {
                            Image(systemName: reminder.category.icon)
                                .foregroundColor(reminder.category.color)
                            Text(reminder.category.rawValue)
                        }
                    })

                    DetailRow(title: "Priority", content: {
                        Text(reminder.priority.rawValue)
                            .foregroundColor(reminder.priority.color)
                            .fontWeight(.medium)
                    })

                    if let dueDate = reminder.dueDate {
                        DetailRow(title: "Due Date", content: {
                            VStack(alignment: .leading, spacing: 2) {
                                Text(dueDate, style: .date)
                                Text(dueDate, style: .time)
                                    .font(.caption)
                                    .foregroundColor(.secondary)

                                if reminder.isOverdue && !reminder.isCompleted {
                                    Text("Overdue")
                                        .font(.caption)
                                        .fontWeight(.medium)
                                        .foregroundColor(.red)
                                }
                            }
                        })
                    }

                    if reminder.isCompleted, let completedAt = reminder.completedAt {
                        DetailRow(title: "Completed", content: {
                            VStack(alignment: .leading, spacing: 2) {
                                Text(completedAt, style: .date)
                                Text(completedAt, style: .time)
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        })
                    }
                }

                // Notes
                if !reminder.notes.isEmpty {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Notes")
                            .font(.headline)

                        Text(reminder.notes)
                            .font(.body)
                            .padding()
                            .background(Color(NSColor.controlBackgroundColor))
                            .clipShape(RoundedRectangle(cornerRadius: 8))
                    }
                }

                Spacer()
            }
            .padding()
        }
        .navigationTitle("Reminder Details")
        .sheet(isPresented: $showingEditSheet) {
            EditReminderView(reminder: reminder)
                .environmentObject(reminderStore)
        }
    }
}

struct DetailRow<Content: View>: View {
    let title: String
    let content: () -> Content

    var body: some View {
        HStack(alignment: .top) {
            Text(title)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.secondary)
                .frame(width: 80, alignment: .leading)

            content()

            Spacer()
        }
    }
}

#Preview {
    ContentView()
        .environmentObject(ReminderStore())
}
