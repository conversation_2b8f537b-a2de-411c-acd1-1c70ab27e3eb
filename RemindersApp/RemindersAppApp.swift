//
//  RemindersAppApp.swift
//  RemindersApp
//
//  Created by User on 2025-07-25.
//

import SwiftUI

@main
struct RemindersAppApp: App {
    @StateObject private var reminderStore = ReminderStore()
    
    var body: some Scene {
        WindowGroup {
            ContentView()
                .environmentObject(reminderStore)
                .frame(minWidth: 800, minHeight: 600)
        }
        .windowStyle(.titleBar)
        .windowToolbarStyle(.unified)
        .commands {
            AppCommands()
        }
        
        Settings {
            SettingsView()
                .environmentObject(reminderStore)
        }
    }
}

struct AppCommands: Commands {
    @FocusedValue(\.reminderStore) var reminderStore
    
    var body: some Commands {
        CommandMenu("Reminders") {
            Button("New Reminder") {
                // This will be handled by the main view
            }
            .keyboardShortcut("n", modifiers: .command)
            
            Divider()
            
            But<PERSON>("Clear Completed") {
                reminderStore?.clearCompleted()
            }
            .keyboardShortcut("k", modifiers: [.command, .shift])
            
            But<PERSON>("Export Reminders...") {
                // This will be handled by the main view
            }
            .keyboardShortcut("e", modifiers: .command)
            
            But<PERSON>("Import Reminders...") {
                // This will be handled by the main view
            }
            .keyboardShortcut("i", modifiers: .command)
        }
        
        CommandMenu("View") {
            Button("Show All") {
                reminderStore?.selectedFilter = .all
            }
            .keyboardShortcut("1", modifiers: .command)
            
            Button("Show Pending") {
                reminderStore?.selectedFilter = .pending
            }
            .keyboardShortcut("2", modifiers: .command)
            
            Button("Show Completed") {
                reminderStore?.selectedFilter = .completed
            }
            .keyboardShortcut("3", modifiers: .command)
            
            Button("Show Overdue") {
                reminderStore?.selectedFilter = .overdue
            }
            .keyboardShortcut("4", modifiers: .command)
            
            Divider()
            
            Button("Clear Filters") {
                reminderStore?.clearFilters()
            }
            .keyboardShortcut("r", modifiers: .command)
        }
    }
}

// MARK: - Focused Values
struct ReminderStoreFocusedValueKey: FocusedValueKey {
    typealias Value = ReminderStore
}

extension FocusedValues {
    var reminderStore: ReminderStore? {
        get { self[ReminderStoreFocusedValueKey.self] }
        set { self[ReminderStoreFocusedValueKey.self] = newValue }
    }
}

// MARK: - Settings View
struct SettingsView: View {
    @EnvironmentObject var reminderStore: ReminderStore
    @AppStorage("showCompletedReminders") private var showCompletedReminders = true
    @AppStorage("enableNotifications") private var enableNotifications = true
    @AppStorage("defaultCategory") private var defaultCategory = ReminderCategory.personal.rawValue
    @AppStorage("defaultPriority") private var defaultPriority = ReminderPriority.medium.rawValue
    
    var body: some View {
        TabView {
            GeneralSettingsView(
                showCompletedReminders: $showCompletedReminders,
                enableNotifications: $enableNotifications,
                defaultCategory: $defaultCategory,
                defaultPriority: $defaultPriority
            )
            .tabItem {
                Label("General", systemImage: "gear")
            }
            
            AboutSettingsView()
                .tabItem {
                    Label("About", systemImage: "info.circle")
                }
        }
        .frame(width: 450, height: 300)
    }
}

struct GeneralSettingsView: View {
    @Binding var showCompletedReminders: Bool
    @Binding var enableNotifications: Bool
    @Binding var defaultCategory: String
    @Binding var defaultPriority: String
    
    var body: some View {
        Form {
            Section("Display") {
                Toggle("Show completed reminders", isOn: $showCompletedReminders)
                Toggle("Enable notifications", isOn: $enableNotifications)
            }
            
            Section("Defaults") {
                Picker("Default category:", selection: $defaultCategory) {
                    ForEach(ReminderCategory.allCases, id: \.rawValue) { category in
                        Text(category.rawValue).tag(category.rawValue)
                    }
                }
                
                Picker("Default priority:", selection: $defaultPriority) {
                    ForEach(ReminderPriority.allCases, id: \.rawValue) { priority in
                        Text(priority.rawValue).tag(priority.rawValue)
                    }
                }
            }
        }
        .padding()
    }
}

struct AboutSettingsView: View {
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "checklist")
                .font(.system(size: 64))
                .foregroundColor(.accentColor)
            
            Text("Reminders")
                .font(.title)
                .fontWeight(.bold)
            
            Text("Version 1.0")
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            Text("A clean and simple reminders app for macOS")
                .font(.body)
                .multilineTextAlignment(.center)
                .foregroundColor(.secondary)
            
            Spacer()
        }
        .padding()
    }
}
