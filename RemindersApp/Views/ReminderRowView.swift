//
//  ReminderRowView.swift
//  RemindersApp
//
//  Created by User on 2025-07-25.
//

import SwiftUI

struct ReminderRowView: View {
    @EnvironmentObject var reminderStore: ReminderStore
    let reminder: Reminder
    @State private var showingEditSheet = false
    
    var body: some View {
        HStack(spacing: 12) {
            // Completion checkbox
            Button(action: {
                reminderStore.toggleCompletion(for: reminder)
            }) {
                Image(systemName: reminder.isCompleted ? "checkmark.circle.fill" : "circle")
                    .font(.title2)
                    .foregroundColor(reminder.isCompleted ? .green : .secondary)
            }
            .buttonStyle(.plain)
            .help(reminder.isCompleted ? "Mark as pending" : "Mark as complete")
            
            VStack(alignment: .leading, spacing: 4) {
                // Reminder text
                Text(reminder.text)
                    .font(.body)
                    .fontWeight(.medium)
                    .strikethrough(reminder.isCompleted)
                    .foregroundColor(reminder.isCompleted ? .secondary : .primary)
                    .lineLimit(2)
                
                // Metadata row
                HStack(spacing: 8) {
                    // Category badge
                    CategoryBadge(category: reminder.category)
                    
                    // Priority indicator
                    if reminder.priority != .medium {
                        PriorityBadge(priority: reminder.priority)
                    }
                    
                    // Due date
                    if let dueDateString = reminder.dueDateString {
                        DueDateBadge(
                            text: dueDateString,
                            isOverdue: reminder.isOverdue,
                            isCompleted: reminder.isCompleted
                        )
                    }
                    
                    Spacer()
                }
            }
            
            Spacer()
            
            // Action buttons (shown on hover)
            HStack(spacing: 4) {
                Button(action: { showingEditSheet = true }) {
                    Image(systemName: "pencil")
                        .font(.caption)
                }
                .buttonStyle(.plain)
                .help("Edit reminder")
                
                Button(action: {
                    reminderStore.deleteReminder(reminder)
                }) {
                    Image(systemName: "trash")
                        .font(.caption)
                        .foregroundColor(.red)
                }
                .buttonStyle(.plain)
                .help("Delete reminder")
            }
            .opacity(0.7)
        }
        .padding(.vertical, 4)
        .contentShape(Rectangle())
        .contextMenu {
            Button(reminder.isCompleted ? "Mark as Pending" : "Mark as Complete") {
                reminderStore.toggleCompletion(for: reminder)
            }
            
            Button("Edit") {
                showingEditSheet = true
            }
            
            Divider()
            
            Button("Delete", role: .destructive) {
                reminderStore.deleteReminder(reminder)
            }
        }
        .sheet(isPresented: $showingEditSheet) {
            EditReminderView(reminder: reminder)
                .environmentObject(reminderStore)
        }
    }
}

struct CategoryBadge: View {
    let category: ReminderCategory
    
    var body: some View {
        HStack(spacing: 4) {
            Image(systemName: category.icon)
                .font(.caption2)
            Text(category.rawValue)
                .font(.caption)
        }
        .foregroundColor(category.color)
        .padding(.horizontal, 6)
        .padding(.vertical, 2)
        .background(category.color.opacity(0.1))
        .clipShape(Capsule())
    }
}

struct PriorityBadge: View {
    let priority: ReminderPriority
    
    var body: some View {
        Text(priority.rawValue)
            .font(.caption)
            .fontWeight(.medium)
            .foregroundColor(priority.color)
            .padding(.horizontal, 6)
            .padding(.vertical, 2)
            .background(priority.color.opacity(0.1))
            .clipShape(Capsule())
    }
}

struct DueDateBadge: View {
    let text: String
    let isOverdue: Bool
    let isCompleted: Bool
    
    var body: some View {
        HStack(spacing: 2) {
            Image(systemName: "calendar")
                .font(.caption2)
            Text(text)
                .font(.caption)
        }
        .foregroundColor(badgeColor)
        .padding(.horizontal, 6)
        .padding(.vertical, 2)
        .background(badgeColor.opacity(0.1))
        .clipShape(Capsule())
    }
    
    private var badgeColor: Color {
        if isCompleted {
            return .secondary
        } else if isOverdue {
            return .red
        } else if text == "Today" {
            return .orange
        } else if text == "Tomorrow" {
            return .blue
        } else {
            return .secondary
        }
    }
}

// MARK: - Edit Reminder View
struct EditReminderView: View {
    @EnvironmentObject var reminderStore: ReminderStore
    @Environment(\.dismiss) private var dismiss
    
    let reminder: Reminder
    @State private var text: String
    @State private var dueDate: Date
    @State private var hasDueDate: Bool
    @State private var category: ReminderCategory
    @State private var priority: ReminderPriority
    @State private var notes: String
    
    init(reminder: Reminder) {
        self.reminder = reminder
        self._text = State(initialValue: reminder.text)
        self._dueDate = State(initialValue: reminder.dueDate ?? Date())
        self._hasDueDate = State(initialValue: reminder.dueDate != nil)
        self._category = State(initialValue: reminder.category)
        self._priority = State(initialValue: reminder.priority)
        self._notes = State(initialValue: reminder.notes)
    }
    
    var body: some View {
        NavigationStack {
            Form {
                Section("Details") {
                    TextField("Reminder text", text: $text, axis: .vertical)
                        .lineLimit(3...6)
                    
                    TextEditor(text: $notes)
                        .frame(minHeight: 60)
                        .overlay(
                            Text("Notes (optional)")
                                .foregroundColor(.secondary)
                                .opacity(notes.isEmpty ? 1 : 0)
                                .allowsHitTesting(false),
                            alignment: .topLeading
                        )
                }
                
                Section("Organization") {
                    Picker("Category", selection: $category) {
                        ForEach(ReminderCategory.allCases, id: \.rawValue) { cat in
                            Label(cat.rawValue, systemImage: cat.icon)
                                .tag(cat)
                        }
                    }
                    
                    Picker("Priority", selection: $priority) {
                        ForEach(ReminderPriority.allCases, id: \.rawValue) { pri in
                            Text(pri.rawValue).tag(pri)
                        }
                    }
                }
                
                Section("Due Date") {
                    Toggle("Set due date", isOn: $hasDueDate)
                    
                    if hasDueDate {
                        DatePicker("Due date", selection: $dueDate, displayedComponents: [.date, .hourAndMinute])
                    }
                }
            }
            .navigationTitle("Edit Reminder")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .confirmationAction) {
                    Button("Save") {
                        saveReminder()
                    }
                    .disabled(text.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
                }
            }
        }
        .frame(width: 400, height: 500)
    }
    
    private func saveReminder() {
        var updatedReminder = reminder
        updatedReminder.text = text.trimmingCharacters(in: .whitespacesAndNewlines)
        updatedReminder.dueDate = hasDueDate ? dueDate : nil
        updatedReminder.category = category
        updatedReminder.priority = priority
        updatedReminder.notes = notes.trimmingCharacters(in: .whitespacesAndNewlines)
        
        reminderStore.updateReminder(updatedReminder)
        dismiss()
    }
}

#Preview {
    List {
        ReminderRowView(reminder: Reminder.sampleData[0])
        ReminderRowView(reminder: Reminder.sampleData[1])
        ReminderRowView(reminder: Reminder.sampleData[4])
    }
    .environmentObject(ReminderStore())
}
