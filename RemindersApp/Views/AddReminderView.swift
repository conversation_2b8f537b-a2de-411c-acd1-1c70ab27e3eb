//
//  AddReminderView.swift
//  RemindersApp
//
//  Created by User on 2025-07-25.
//

import SwiftUI
import UniformTypeIdentifiers

struct AddReminderView: View {
    @EnvironmentObject var reminderStore: ReminderStore
    @Environment(\.dismiss) private var dismiss
    
    @State private var text = ""
    @State private var dueDate = Date()
    @State private var hasDueDate = false
    @State private var category = ReminderCategory.personal
    @State private var priority = ReminderPriority.medium
    @State private var notes = ""
    
    @AppStorage("defaultCategory") private var defaultCategory = ReminderCategory.personal.rawValue
    @AppStorage("defaultPriority") private var defaultPriority = ReminderPriority.medium.rawValue
    
    var body: some View {
        NavigationStack {
            VStack(spacing: 0) {
                Form {
                    Section("Details") {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("What do you need to remember?")
                                .font(.subheadline)
                                .fontWeight(.medium)

                            TextField("Enter reminder text", text: $text, axis: .vertical)
                                .textFieldStyle(.roundedBorder)
                                .lineLimit(2...4)
                                .onSubmit {
                                    if !text.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                                        addReminder()
                                    }
                                }
                        }

                        VStack(alignment: .leading, spacing: 8) {
                            Text("Notes (optional)")
                                .font(.subheadline)
                                .fontWeight(.medium)

                            TextEditor(text: $notes)
                                .frame(height: 60)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 6)
                                        .stroke(Color.secondary.opacity(0.3), lineWidth: 1)
                                )
                                .overlay(
                                    Group {
                                        if notes.isEmpty {
                                            Text("Add additional notes...")
                                                .foregroundColor(.secondary)
                                                .padding(.horizontal, 8)
                                                .padding(.vertical, 8)
                                                .allowsHitTesting(false)
                                        }
                                    },
                                    alignment: .topLeading
                                )
                        }
                    }

                    Section("Organization") {
                        HStack {
                            Text("Category")
                                .frame(width: 80, alignment: .leading)

                            Picker("Category", selection: $category) {
                                ForEach(ReminderCategory.allCases, id: \.rawValue) { cat in
                                    Label(cat.rawValue, systemImage: cat.icon)
                                        .tag(cat)
                                }
                            }
                            .pickerStyle(.menu)
                        }

                        HStack {
                            Text("Priority")
                                .frame(width: 80, alignment: .leading)

                            Picker("Priority", selection: $priority) {
                                ForEach(ReminderPriority.allCases, id: \.rawValue) { pri in
                                    Text(pri.rawValue).tag(pri)
                                }
                            }
                            .pickerStyle(.menu)
                        }
                    }

                    Section("Due Date") {
                        Toggle("Set due date", isOn: $hasDueDate)

                        if hasDueDate {
                            DatePicker("Due date", selection: $dueDate, displayedComponents: [.date, .hourAndMinute])
                                .datePickerStyle(.compact)
                        }
                    }

                    Section("Quick Templates") {
                        QuickActionButtons(
                            text: $text,
                            category: $category,
                            priority: $priority,
                            hasDueDate: $hasDueDate,
                            dueDate: $dueDate
                        )
                    }
                }
                .formStyle(.grouped)
            }
            .navigationTitle("New Reminder")
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .confirmationAction) {
                    Button("Add") {
                        addReminder()
                    }
                    .disabled(text.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
                    .keyboardShortcut(.return, modifiers: .command)
                }
            }
        }
        .frame(width: 500, height: 650)
        .onAppear {
            loadDefaults()
        }
    }
    
    private func loadDefaults() {
        if let defaultCat = ReminderCategory.allCases.first(where: { $0.rawValue == defaultCategory }) {
            category = defaultCat
        }
        if let defaultPri = ReminderPriority.allCases.first(where: { $0.rawValue == defaultPriority }) {
            priority = defaultPri
        }
    }
    
    private func addReminder() {
        let trimmedText = text.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedText.isEmpty else { return }
        
        let reminder = Reminder(
            text: trimmedText,
            dueDate: hasDueDate ? dueDate : nil,
            category: category,
            priority: priority,
            notes: notes.trimmingCharacters(in: .whitespacesAndNewlines)
        )
        
        reminderStore.addReminder(reminder)
        dismiss()
    }
}

struct QuickActionButtons: View {
    @Binding var text: String
    @Binding var category: ReminderCategory
    @Binding var priority: ReminderPriority
    @Binding var hasDueDate: Bool
    @Binding var dueDate: Date
    
    var body: some View {
        LazyVGrid(columns: [
            GridItem(.flexible()),
            GridItem(.flexible())
        ], spacing: 12) {
                QuickActionButton(
                    title: "Today",
                    icon: "calendar.badge.clock",
                    color: .orange
                ) {
                    hasDueDate = true
                    dueDate = Calendar.current.startOfDay(for: Date())
                        .addingTimeInterval(18 * 3600) // 6 PM
                }
                
                QuickActionButton(
                    title: "Tomorrow",
                    icon: "calendar.badge.plus",
                    color: .blue
                ) {
                    hasDueDate = true
                    dueDate = Calendar.current.date(byAdding: .day, value: 1, to: Date()) ?? Date()
                }
                
                QuickActionButton(
                    title: "High Priority",
                    icon: "exclamationmark.triangle.fill",
                    color: .red
                ) {
                    priority = .high
                }
                
                QuickActionButton(
                    title: "Work Task",
                    icon: "briefcase.fill",
                    color: .orange
                ) {
                    category = .work
                    if text.isEmpty {
                        text = "Complete "
                    }
                }
                
                QuickActionButton(
                    title: "Shopping",
                    icon: "cart.fill",
                    color: .green
                ) {
                    category = .shopping
                    if text.isEmpty {
                        text = "Buy "
                    }
                }
                
                QuickActionButton(
                    title: "Health",
                    icon: "heart.fill",
                    color: .red
                ) {
                    category = .health
                    if text.isEmpty {
                        text = "Schedule "
                    }
                }
            }
        }
    }

struct QuickActionButton: View {
    let title: String
    let icon: String
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 6) {
                Image(systemName: icon)
                    .font(.system(size: 20))
                    .foregroundColor(color)

                Text(title)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
            }
            .frame(maxWidth: .infinity, minHeight: 50)
            .background(color.opacity(0.1))
            .clipShape(RoundedRectangle(cornerRadius: 8))
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(color.opacity(0.3), lineWidth: 1)
            )
        }
        .buttonStyle(.plain)
    }
}

// MARK: - Import/Export View
struct ImportExportView: View {
    @EnvironmentObject var reminderStore: ReminderStore
    @Environment(\.dismiss) private var dismiss
    @State private var showingFileImporter = false
    @State private var showingFileExporter = false
    @State private var exportData: Data?
    @State private var alertMessage = ""
    @State private var showingAlert = false
    
    var body: some View {
        NavigationStack {
            VStack(spacing: 30) {
                VStack(spacing: 20) {
                    Image(systemName: "square.and.arrow.up.on.square")
                        .font(.system(size: 48))
                        .foregroundColor(.accentColor)
                    
                    Text("Import & Export")
                        .font(.title2)
                        .fontWeight(.semibold)
                }
                
                VStack(spacing: 16) {
                    Button(action: exportReminders) {
                        Label("Export Reminders", systemImage: "square.and.arrow.up")
                            .frame(maxWidth: .infinity)
                    }
                    .buttonStyle(.borderedProminent)
                    .controlSize(.large)
                    
                    Button(action: { showingFileImporter = true }) {
                        Label("Import Reminders", systemImage: "square.and.arrow.down")
                            .frame(maxWidth: .infinity)
                    }
                    .buttonStyle(.bordered)
                    .controlSize(.large)
                }
                
                VStack(alignment: .leading, spacing: 8) {
                    Text("Notes:")
                        .font(.subheadline)
                        .fontWeight(.medium)
                    
                    Text("• Export creates a JSON file with all your reminders")
                    Text("• Import will replace all current reminders")
                    Text("• Make sure to backup before importing")
                }
                .font(.caption)
                .foregroundColor(.secondary)
                .frame(maxWidth: .infinity, alignment: .leading)
                
                Spacer()
            }
            .padding()
            .navigationTitle("Import & Export")
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
        .frame(width: 400, height: 350)
        .fileImporter(
            isPresented: $showingFileImporter,
            allowedContentTypes: [.json],
            allowsMultipleSelection: false
        ) { result in
            handleImport(result)
        }
        .fileExporter(
            isPresented: $showingFileExporter,
            document: JSONDocument(data: exportData ?? Data()),
            contentType: .json,
            defaultFilename: "reminders-\(DateFormatter.filenameDateFormatter.string(from: Date()))"
        ) { result in
            handleExport(result)
        }
        .alert("Import/Export", isPresented: $showingAlert) {
            Button("OK") { }
        } message: {
            Text(alertMessage)
        }
    }
    
    private func exportReminders() {
        guard let data = reminderStore.exportReminders() else {
            alertMessage = "Failed to export reminders"
            showingAlert = true
            return
        }
        
        exportData = data
        showingFileExporter = true
    }
    
    private func handleImport(_ result: Result<[URL], Error>) {
        switch result {
        case .success(let urls):
            guard let url = urls.first else { return }
            
            do {
                let data = try Data(contentsOf: url)
                if reminderStore.importReminders(from: data) {
                    alertMessage = "Reminders imported successfully"
                    dismiss()
                } else {
                    alertMessage = "Failed to import reminders. Please check the file format."
                }
                showingAlert = true
            } catch {
                alertMessage = "Failed to read file: \(error.localizedDescription)"
                showingAlert = true
            }
            
        case .failure(let error):
            alertMessage = "Import failed: \(error.localizedDescription)"
            showingAlert = true
        }
    }
    
    private func handleExport(_ result: Result<URL, Error>) {
        switch result {
        case .success:
            alertMessage = "Reminders exported successfully"
        case .failure(let error):
            alertMessage = "Export failed: \(error.localizedDescription)"
        }
        showingAlert = true
    }
}

// MARK: - Helper Types
struct JSONDocument: FileDocument {
    static var readableContentTypes: [UTType] { [.json] }
    
    var data: Data
    
    init(data: Data) {
        self.data = data
    }
    
    init(configuration: ReadConfiguration) throws {
        data = configuration.file.regularFileContents ?? Data()
    }
    
    func fileWrapper(configuration: WriteConfiguration) throws -> FileWrapper {
        FileWrapper(regularFileWithContents: data)
    }
}

extension DateFormatter {
    static let filenameDateFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        return formatter
    }()
}

#Preview {
    AddReminderView()
        .environmentObject(ReminderStore())
}
