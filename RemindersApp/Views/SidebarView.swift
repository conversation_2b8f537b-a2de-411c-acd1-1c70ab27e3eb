//
//  SidebarView.swift
//  RemindersApp
//
//  Created by User on 2025-07-25.
//

import Swift<PERSON>

struct SidebarView: View {
    @EnvironmentObject var reminderStore: ReminderStore
    @Binding var selectedReminder: Reminder?
    
    var body: some View {
        List {
            Section("Filters") {
                ForEach(FilterOption.allCases, id: \.rawValue) { filter in
                    SidebarFilterRow(
                        filter: filter,
                        count: countForFilter(filter),
                        isSelected: reminderStore.selectedFilter == filter
                    )
                    .onTapGesture {
                        reminderStore.selectedFilter = filter
                        reminderStore.selectedCategory = nil
                        selectedReminder = nil
                    }
                }
            }
            
            Section("Categories") {
                ForEach(ReminderCategory.allCases, id: \.rawValue) { category in
                    SidebarCategoryRow(
                        category: category,
                        count: countForCategory(category),
                        isSelected: reminderStore.selectedCategory == category
                    )
                    .onTapGesture {
                        reminderStore.selectedCategory = category
                        reminderStore.selectedFilter = .all
                        selectedReminder = nil
                    }
                }
            }

            // Cat illustration section
            Section {
                HStack {
                    Spacer()
                    CatIllustration()
                    Spacer()
                }
                .padding(.vertical, 30)
            }
        }
        .listStyle(.sidebar)
        .navigationTitle("Reminders")
    }
    
    private func countForFilter(_ filter: FilterOption) -> Int {
        switch filter {
        case .all:
            return reminderStore.reminders.count
        case .pending:
            return reminderStore.reminders.filter { !$0.isCompleted }.count
        case .completed:
            return reminderStore.reminders.filter { $0.isCompleted }.count
        case .overdue:
            return reminderStore.reminders.filter { $0.isOverdue }.count
        case .today:
            return reminderStore.reminders.filter { reminder in
                guard let dueDate = reminder.dueDate else { return false }
                return Calendar.current.isDateInToday(dueDate)
            }.count
        case .upcoming:
            return reminderStore.reminders.filter { reminder in
                guard let dueDate = reminder.dueDate, !reminder.isCompleted else { return false }
                return dueDate > Date()
            }.count
        }
    }
    
    private func countForCategory(_ category: ReminderCategory) -> Int {
        return reminderStore.reminders.filter { $0.category == category }.count
    }
}

struct SidebarFilterRow: View {
    let filter: FilterOption
    let count: Int
    let isSelected: Bool
    
    var body: some View {
        HStack {
            Image(systemName: filter.systemImage)
                .foregroundColor(filter.color)
                .frame(width: 20)
            
            Text(filter.rawValue)
                .fontWeight(isSelected ? .medium : .regular)
            
            Spacer()
            
            if count > 0 {
                Text("\(count)")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(Color.secondary.opacity(0.2))
                    .clipShape(Capsule())
            }
        }
        .contentShape(Rectangle())
    }
}

struct SidebarCategoryRow: View {
    let category: ReminderCategory
    let count: Int
    let isSelected: Bool
    
    var body: some View {
        HStack {
            Image(systemName: category.icon)
                .foregroundColor(category.color)
                .frame(width: 20)
            
            Text(category.rawValue)
                .fontWeight(isSelected ? .medium : .regular)
            
            Spacer()
            
            if count > 0 {
                Text("\(count)")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(Color.secondary.opacity(0.2))
                    .clipShape(Capsule())
            }
        }
        .contentShape(Rectangle())
    }
}

#Preview {
    NavigationSplitView {
        SidebarView(selectedReminder: .constant(nil))
            .environmentObject(ReminderStore())
    } content: {
        Text("Content")
    } detail: {
        Text("Detail")
    }
}

// MARK: - Cat Illustration
struct CatIllustration: View {
    var body: some View {
        VStack(spacing: 8) {
            // Use SF Symbol for a clean, recognizable cat
            Image(systemName: "cat.fill")
                .font(.system(size: 32, weight: .light))
                .foregroundColor(.secondary.opacity(0.4))

            Text("Purrfect reminders")
                .font(.caption2)
                .foregroundColor(.secondary.opacity(0.4))
                .multilineTextAlignment(.center)
        }
    }
}


