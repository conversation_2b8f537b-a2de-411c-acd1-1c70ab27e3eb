//
//  SidebarView.swift
//  RemindersApp
//
//  Created by User on 2025-07-25.
//

import SwiftUI

struct SidebarView: View {
    @EnvironmentObject var reminderStore: ReminderStore
    @Binding var selectedReminder: Reminder?
    
    var body: some View {
        List {
            Section("Filters") {
                ForEach(FilterOption.allCases, id: \.rawValue) { filter in
                    SidebarFilterRow(
                        filter: filter,
                        count: countForFilter(filter),
                        isSelected: reminderStore.selectedFilter == filter
                    )
                    .onTapGesture {
                        reminderStore.selectedFilter = filter
                        reminderStore.selectedCategory = nil
                        selectedReminder = nil
                    }
                }
            }
            
            Section("Categories") {
                ForEach(ReminderCategory.allCases, id: \.rawValue) { category in
                    SidebarCategoryRow(
                        category: category,
                        count: countForCategory(category),
                        isSelected: reminderStore.selectedCategory == category
                    )
                    .onTapGesture {
                        reminderStore.selectedCategory = category
                        reminderStore.selectedFilter = .all
                        selectedReminder = nil
                    }
                }
            }

            // Cat illustration section
            Section {
                VStack {
                    Spacer()

                    CatIllustration()
                        .frame(width: 120, height: 100)
                        .opacity(0.3)
                        .padding(.bottom, 20)
                }
                .frame(height: 120)
            }
        }
        .listStyle(.sidebar)
        .navigationTitle("Reminders")
    }
    
    private func countForFilter(_ filter: FilterOption) -> Int {
        switch filter {
        case .all:
            return reminderStore.reminders.count
        case .pending:
            return reminderStore.reminders.filter { !$0.isCompleted }.count
        case .completed:
            return reminderStore.reminders.filter { $0.isCompleted }.count
        case .overdue:
            return reminderStore.reminders.filter { $0.isOverdue }.count
        case .today:
            return reminderStore.reminders.filter { reminder in
                guard let dueDate = reminder.dueDate else { return false }
                return Calendar.current.isDateInToday(dueDate)
            }.count
        case .upcoming:
            return reminderStore.reminders.filter { reminder in
                guard let dueDate = reminder.dueDate, !reminder.isCompleted else { return false }
                return dueDate > Date()
            }.count
        }
    }
    
    private func countForCategory(_ category: ReminderCategory) -> Int {
        return reminderStore.reminders.filter { $0.category == category }.count
    }
}

struct SidebarFilterRow: View {
    let filter: FilterOption
    let count: Int
    let isSelected: Bool
    
    var body: some View {
        HStack {
            Image(systemName: filter.systemImage)
                .foregroundColor(filter.color)
                .frame(width: 20)
            
            Text(filter.rawValue)
                .fontWeight(isSelected ? .medium : .regular)
            
            Spacer()
            
            if count > 0 {
                Text("\(count)")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(Color.secondary.opacity(0.2))
                    .clipShape(Capsule())
            }
        }
        .contentShape(Rectangle())
    }
}

struct SidebarCategoryRow: View {
    let category: ReminderCategory
    let count: Int
    let isSelected: Bool
    
    var body: some View {
        HStack {
            Image(systemName: category.icon)
                .foregroundColor(category.color)
                .frame(width: 20)
            
            Text(category.rawValue)
                .fontWeight(isSelected ? .medium : .regular)
            
            Spacer()
            
            if count > 0 {
                Text("\(count)")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(Color.secondary.opacity(0.2))
                    .clipShape(Capsule())
            }
        }
        .contentShape(Rectangle())
    }
}

#Preview {
    NavigationSplitView {
        SidebarView(selectedReminder: .constant(nil))
            .environmentObject(ReminderStore())
    } content: {
        Text("Content")
    } detail: {
        Text("Detail")
    }
}

// MARK: - Cat Illustration
struct CatIllustration: View {
    var body: some View {
        ZStack {
            // Cat body
            Ellipse()
                .fill(Color.secondary.opacity(0.4))
                .frame(width: 60, height: 40)
                .offset(y: 15)

            // Cat head
            Circle()
                .fill(Color.secondary.opacity(0.4))
                .frame(width: 45, height: 45)
                .offset(y: -5)

            // Cat ears
            VStack {
                HStack(spacing: 20) {
                    Triangle()
                        .fill(Color.secondary.opacity(0.4))
                        .frame(width: 12, height: 15)
                    Triangle()
                        .fill(Color.secondary.opacity(0.4))
                        .frame(width: 12, height: 15)
                }
                Spacer()
            }
            .offset(y: -18)

            // Cat face
            VStack(spacing: 2) {
                // Eyes
                HStack(spacing: 8) {
                    Circle()
                        .fill(Color.primary.opacity(0.6))
                        .frame(width: 3, height: 3)
                    Circle()
                        .fill(Color.primary.opacity(0.6))
                        .frame(width: 3, height: 3)
                }

                // Nose
                Triangle()
                    .fill(Color.primary.opacity(0.4))
                    .frame(width: 2, height: 2)
                    .rotationEffect(.degrees(180))

                // Mouth
                Path { path in
                    path.move(to: CGPoint(x: 0, y: 0))
                    path.addCurve(to: CGPoint(x: -6, y: 4),
                                control1: CGPoint(x: -2, y: 2),
                                control2: CGPoint(x: -4, y: 4))
                    path.move(to: CGPoint(x: 0, y: 0))
                    path.addCurve(to: CGPoint(x: 6, y: 4),
                                control1: CGPoint(x: 2, y: 2),
                                control2: CGPoint(x: 4, y: 4))
                }
                .stroke(Color.primary.opacity(0.4), lineWidth: 1)
                .frame(width: 12, height: 4)
            }
            .offset(y: -5)

            // Cat tail
            Path { path in
                path.move(to: CGPoint(x: 30, y: 15))
                path.addCurve(to: CGPoint(x: 45, y: -10),
                            control1: CGPoint(x: 35, y: 10),
                            control2: CGPoint(x: 40, y: 0))
            }
            .stroke(Color.secondary.opacity(0.4), lineWidth: 4)
        }
        .frame(width: 80, height: 60)
    }
}

// Helper shape for triangular ears and nose
struct Triangle: Shape {
    func path(in rect: CGRect) -> Path {
        var path = Path()
        path.move(to: CGPoint(x: rect.midX, y: rect.minY))
        path.addLine(to: CGPoint(x: rect.minX, y: rect.maxY))
        path.addLine(to: CGPoint(x: rect.maxX, y: rect.maxY))
        path.addLine(to: CGPoint(x: rect.midX, y: rect.minY))
        return path
    }
}
