# Clean Reminders App for macOS

A beautiful, simple, and powerful reminders application designed specifically for macOS users. Built with modern web technologies for a native-like experience.

## Features

### Core Functionality
- ✅ **Add & Manage Reminders** - Create, edit, and delete reminders with ease
- 📅 **Due Dates** - Set due dates and get visual indicators for overdue items
- 🏷️ **Categories** - Organize reminders with Personal, Work, Shopping, and Health categories
- ⭐ **Priority Levels** - Set High, Medium, or Low priority for better organization
- ✔️ **Mark Complete** - Track your progress with completion status

### Advanced Features
- 🔍 **Smart Search** - Find reminders quickly with real-time search
- 🎛️ **Filtering** - Filter by category, status, or search terms
- 📊 **Sorting** - Sort by creation date, due date, priority, or category
- 🌙 **Dark Mode** - Beautiful dark theme that matches macOS
- 💾 **Auto-Save** - Your data is automatically saved locally
- 📤 **Export/Import** - Backup and restore your reminders as JSON files
- ⌨️ **Keyboard Shortcuts** - Work efficiently with keyboard shortcuts

### User Experience
- 🎨 **Clean Design** - Minimalist interface inspired by macOS design language
- 📱 **Responsive** - Works perfectly on different screen sizes
- ⚡ **Fast Performance** - No external dependencies, pure vanilla JavaScript
- 🔒 **Privacy First** - All data stored locally, no cloud sync required

## Installation & Setup

### Quick Start
1. **Download** - Clone or download this repository to your Mac
2. **Open** - Double-click `index.html` to open in your default browser
3. **Bookmark** - Add to your bookmarks or dock for easy access

### Recommended Setup
For the best experience, we recommend using this app in a dedicated browser window:

1. Open the app in Safari, Chrome, or Firefox
2. Create a bookmark for easy access
3. For a more app-like experience:
   - **Safari**: Add to Dock via File → Add to Dock
   - **Chrome**: Install as PWA via the install button in the address bar

## Usage Guide

### Basic Operations

#### Adding Reminders
1. Type your reminder in the main input field
2. Optionally set a due date, category, and priority
3. Press Enter or click "Add" to save

#### Managing Reminders
- **Complete**: Click the ✅ button to mark as done
- **Edit**: Click the ✏️ button to modify details
- **Delete**: Click the 🗑️ button to remove

### Advanced Features

#### Search & Filter
- Click the 🔍 button or press `⌘+K` to open search
- Type to search across all reminder text
- Use dropdown filters for category and status
- Press Escape to close search

#### Keyboard Shortcuts
- `⌘+K` - Open/close search
- `⌘+N` - Focus on new reminder input
- `⌘+A` - Select all reminders (when implemented)
- `⌘+D` - Delete selected reminders (when implemented)
- `Escape` - Close modals and search

#### Sorting
Use the sort dropdown in the header to organize by:
- **Created** - Most recent first (default)
- **Due Date** - Upcoming deadlines first
- **Priority** - High priority items first
- **Category** - Alphabetical by category

#### Export & Import
- **Export**: Click the menu (⋯) → "Export Reminders" to download a JSON backup
- **Import**: Click the menu (⋯) → "Import Reminders" to restore from a JSON file

#### Themes
- Click the 🌙/☀️ button to toggle between light and dark modes
- Your preference is automatically saved

## Technical Details

### Technology Stack
- **HTML5** - Semantic markup for accessibility
- **CSS3** - Modern styling with CSS custom properties
- **Vanilla JavaScript** - No frameworks, pure ES6+ code
- **Local Storage** - Browser-based data persistence

### Browser Compatibility
- Safari 14+
- Chrome 80+
- Firefox 75+
- Edge 80+

### Data Storage
All data is stored locally in your browser using localStorage. This means:
- ✅ Your data stays private and secure
- ✅ No internet connection required
- ✅ Fast performance
- ⚠️ Data is tied to the specific browser and device
- ⚠️ Clearing browser data will remove reminders (use export for backups)

## File Structure

```
reminders-app/
├── index.html          # Main application interface
├── styles.css          # All styling and themes
├── script.js           # Application logic and functionality
└── README.md           # This documentation
```

## Customization

### Adding New Categories
Edit the category options in both `index.html` files:
- Main form: `#reminderCategory`
- Edit modal: `#editReminderCategory`
- Filter dropdown: `#categoryFilter`

### Modifying Colors
Update CSS custom properties in `styles.css`:
- Light theme: `:root` section
- Dark theme: `[data-theme="dark"]` section

### Adding Features
The app is built with a modular class structure. Add new methods to the `RemindersApp` class in `script.js`.

## Troubleshooting

### Common Issues

**Reminders not saving**
- Ensure your browser allows localStorage
- Check if you're in private/incognito mode

**Dark mode not working**
- Clear browser cache and reload
- Check if browser supports CSS custom properties

**Import/Export not working**
- Ensure you're using a modern browser
- Check file permissions for downloads

### Getting Help
This is a standalone application with no external support. However, you can:
- Check browser console for error messages
- Verify browser compatibility
- Try refreshing the page

## License

This project is open source and available under the MIT License. Feel free to modify and customize for your needs.

---

**Enjoy your clean, simple reminders app! 🎉**
