<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reminders</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=SF+Pro+Display:wght@300;400;500;600&display=swap" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="app-header">
            <h1 class="app-title">Reminders</h1>
            <div class="header-controls">
                <div class="sort-controls">
                    <select id="sortSelect" class="sort-select" title="Sort reminders">
                        <option value="created">Sort by Created</option>
                        <option value="date">Sort by Due Date</option>
                        <option value="priority">Sort by Priority</option>
                        <option value="category">Sort by Category</option>
                    </select>
                </div>
                <button class="theme-toggle" id="themeToggle" title="Toggle dark mode">
                    <span class="theme-icon">🌙</span>
                </button>
                <button class="search-toggle" id="searchToggle" title="Search reminders">
                    <span class="search-icon">🔍</span>
                </button>
                <div class="menu-toggle" id="menuToggle" title="More options">
                    <span class="menu-icon">⋯</span>
                    <div class="dropdown-menu" id="dropdownMenu">
                        <button onclick="app.exportReminders()">Export Reminders</button>
                        <label for="importFile" class="import-btn">Import Reminders</label>
                        <input type="file" id="importFile" accept=".json" style="display: none;" onchange="app.importReminders(event)">
                    </div>
                </div>
            </div>
        </header>

        <!-- Search Bar -->
        <div class="search-container" id="searchContainer">
            <input type="text" id="searchInput" placeholder="Search reminders..." class="search-input">
            <div class="filter-controls">
                <select id="categoryFilter" class="filter-select">
                    <option value="">All Categories</option>
                    <option value="personal">Personal</option>
                    <option value="work">Work</option>
                    <option value="shopping">Shopping</option>
                    <option value="health">Health</option>
                </select>
                <select id="statusFilter" class="filter-select">
                    <option value="">All Status</option>
                    <option value="pending">Pending</option>
                    <option value="completed">Completed</option>
                </select>
            </div>
        </div>

        <!-- Add Reminder Form -->
        <div class="add-reminder-container">
            <form class="add-reminder-form" id="addReminderForm">
                <div class="form-row">
                    <input type="text" id="reminderText" placeholder="Add a new reminder..." class="reminder-input" required>
                    <button type="submit" class="add-button">Add</button>
                </div>
                <div class="form-row form-details">
                    <input type="date" id="reminderDate" class="date-input">
                    <select id="reminderCategory" class="category-select">
                        <option value="personal">Personal</option>
                        <option value="work">Work</option>
                        <option value="shopping">Shopping</option>
                        <option value="health">Health</option>
                    </select>
                    <select id="reminderPriority" class="priority-select">
                        <option value="low">Low</option>
                        <option value="medium" selected>Medium</option>
                        <option value="high">High</option>
                    </select>
                </div>
            </form>
        </div>

        <!-- Reminders List -->
        <main class="reminders-container">
            <div class="reminders-list" id="remindersList">
                <!-- Reminders will be dynamically added here -->
            </div>
            
            <!-- Empty State -->
            <div class="empty-state" id="emptyState">
                <div class="empty-icon">📝</div>
                <h3>No reminders yet</h3>
                <p>Add your first reminder above to get started</p>
            </div>
        </main>

        <!-- Stats Footer -->
        <footer class="app-footer">
            <div class="stats">
                <span id="totalCount">0 reminders</span>
                <span id="completedCount">0 completed</span>
            </div>
            <div class="keyboard-shortcuts">
                <small>
                    <strong>Shortcuts:</strong>
                    ⌘+K Search • ⌘+N New • ⌘+A Select All • ⌘+D Delete Selected
                </small>
            </div>
        </footer>
    </div>

    <!-- Edit Modal -->
    <div class="modal-overlay" id="editModal">
        <div class="modal">
            <div class="modal-header">
                <h3>Edit Reminder</h3>
                <button class="modal-close" id="closeModal">&times;</button>
            </div>
            <form class="modal-form" id="editReminderForm">
                <input type="text" id="editReminderText" class="reminder-input" required>
                <div class="form-row">
                    <input type="date" id="editReminderDate" class="date-input">
                    <select id="editReminderCategory" class="category-select">
                        <option value="personal">Personal</option>
                        <option value="work">Work</option>
                        <option value="shopping">Shopping</option>
                        <option value="health">Health</option>
                    </select>
                    <select id="editReminderPriority" class="priority-select">
                        <option value="low">Low</option>
                        <option value="medium">Medium</option>
                        <option value="high">High</option>
                    </select>
                </div>
                <div class="modal-actions">
                    <button type="button" class="cancel-button" id="cancelEdit">Cancel</button>
                    <button type="submit" class="save-button">Save Changes</button>
                </div>
            </form>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
