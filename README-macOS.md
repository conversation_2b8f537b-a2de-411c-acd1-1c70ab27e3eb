# Native macOS Reminders App

A beautiful, native macOS reminders application built with SwiftUI, designed specifically for your iMac with all the modern macOS features you expect.

## 🎯 Features

### Core Functionality
- ✅ **Native macOS Design** - Built with SwiftUI following Apple's Human Interface Guidelines
- 📝 **Create & Manage Reminders** - Add, edit, delete, and complete reminders with ease
- 📅 **Due Dates & Times** - Set specific due dates and times with native date pickers
- 🏷️ **Categories** - Organize with Personal, Work, Shopping, and Health categories
- ⭐ **Priority Levels** - Set High, Medium, or Low priority for better organization
- 📱 **Three-Pane Interface** - Sidebar, list, and detail views for optimal workflow

### Advanced Features
- 🔍 **Smart Search** - Real-time search across reminder text and notes
- 🎛️ **Advanced Filtering** - Filter by status, category, due date, and more
- 📊 **Multiple Sort Options** - Sort by date, priority, category, or alphabetically
- 📝 **Rich Notes** - Add detailed notes to any reminder
- 💾 **Automatic Persistence** - All data saved locally using UserDefaults
- 📤 **Import/Export** - Backup and restore reminders as JSON files

### Native macOS Integration
- 🔔 **Native Notifications** - System notifications for due reminders
- ⌨️ **Keyboard Shortcuts** - Full keyboard navigation and shortcuts
- 🎨 **System Appearance** - Automatically adapts to light/dark mode
- 📋 **Menu Bar Integration** - Native menu commands and shortcuts
- ⚙️ **Settings Panel** - Native preferences window
- 🪟 **Window Management** - Proper window sizing and state restoration

### Keyboard Shortcuts
- `⌘+N` - New reminder
- `⌘+K` - Clear completed reminders
- `⌘+E` - Export reminders
- `⌘+I` - Import reminders
- `⌘+R` - Clear filters
- `⌘+1-4` - Quick filter views
- `⌘+Return` - Save when editing

## 🚀 Getting Started

### Requirements
- macOS 13.0 (Ventura) or later
- Xcode 15.0 or later (for building from source)

### Building the App

1. **Open in Xcode**
   ```bash
   open RemindersApp.xcodeproj
   ```

2. **Configure Signing**
   - Select the RemindersApp target
   - Go to "Signing & Capabilities"
   - Select your development team
   - Update the bundle identifier if needed

3. **Build and Run**
   - Press `⌘+R` to build and run
   - Or use Product → Run from the menu

### Installation Options

#### Option 1: Build from Source
1. Clone/download this repository
2. Open `RemindersApp.xcodeproj` in Xcode
3. Build and run the project
4. The app will launch and be available in your Applications folder

#### Option 2: Archive for Distribution
1. In Xcode, select Product → Archive
2. Once archived, click "Distribute App"
3. Choose "Copy App" to get a .app bundle
4. Move the .app to your Applications folder

## 🎨 App Structure

### Architecture
- **SwiftUI** - Modern declarative UI framework
- **MVVM Pattern** - Clean separation of concerns
- **ObservableObject** - Reactive data flow
- **UserDefaults** - Local data persistence
- **UserNotifications** - Native notification system

### File Organization
```
RemindersApp/
├── RemindersAppApp.swift          # Main app entry point
├── ContentView.swift              # Main three-pane interface
├── Models/
│   └── ReminderModel.swift        # Data models and enums
├── Stores/
│   └── ReminderStore.swift        # Data management and business logic
├── Views/
│   ├── SidebarView.swift          # Left sidebar with filters
│   ├── ReminderRowView.swift      # Individual reminder rows
│   └── AddReminderView.swift      # Add/edit reminder sheets
├── Assets.xcassets/               # App icons and colors
├── Info.plist                     # App configuration
└── RemindersApp.entitlements      # App sandbox permissions
```

## 🔧 Customization

### Adding New Categories
Edit the `ReminderCategory` enum in `ReminderModel.swift`:
```swift
enum ReminderCategory: String, CaseIterable, Codable {
    case personal = "Personal"
    case work = "Work"
    case shopping = "Shopping"
    case health = "Health"
    case newCategory = "New Category"  // Add here
    
    var color: Color {
        switch self {
        // Add color for new category
        case .newCategory: return .purple
        }
    }
    
    var icon: String {
        switch self {
        // Add icon for new category
        case .newCategory: return "star.fill"
        }
    }
}
```

### Modifying Default Settings
Update the `@AppStorage` properties in `SettingsView`:
```swift
@AppStorage("defaultCategory") private var defaultCategory = ReminderCategory.personal.rawValue
@AppStorage("defaultPriority") private var defaultPriority = ReminderPriority.medium.rawValue
```

### Changing App Appearance
- **Colors**: Modify the accent color in `Assets.xcassets/AccentColor.colorset`
- **Icons**: Update app icons in `Assets.xcassets/AppIcon.appiconset`
- **Fonts**: Customize fonts in individual SwiftUI views

## 📱 Usage Guide

### Basic Operations
1. **Adding Reminders**: Click the + button or press ⌘+N
2. **Editing**: Double-click a reminder or select and press Enter
3. **Completing**: Click the circle next to any reminder
4. **Deleting**: Right-click and select Delete, or use the trash icon

### Advanced Features
1. **Search**: Use the search bar at the top of the list
2. **Filtering**: Use the sidebar to filter by status or category
3. **Sorting**: Use the sort menu in the toolbar
4. **Export/Import**: Use the toolbar button or File menu

### Keyboard Navigation
- Use arrow keys to navigate the list
- Press Space to toggle completion
- Press Enter to edit selected reminder
- Press Delete to remove selected reminder

## 🔒 Privacy & Security

### Data Storage
- All data is stored locally using UserDefaults
- No cloud sync or external services
- Data remains on your Mac only
- Export feature allows manual backups

### Permissions
- **Notifications**: Optional, for due date alerts
- **File Access**: Only for import/export operations
- **Sandbox**: App runs in macOS sandbox for security

## 🐛 Troubleshooting

### Common Issues

**App won't launch**
- Ensure you're running macOS 13.0 or later
- Try cleaning and rebuilding in Xcode (⌘+Shift+K, then ⌘+B)

**Notifications not working**
- Check System Preferences → Notifications → Reminders
- Ensure notifications are enabled for the app

**Data not saving**
- Check if the app has proper permissions
- Try restarting the app

**Build errors in Xcode**
- Update to the latest Xcode version
- Clean derived data: Xcode → Preferences → Locations → Derived Data → Delete

### Getting Help
- Check the Issues section of this repository
- Review the code comments for implementation details
- Consult Apple's SwiftUI documentation for framework questions

## 🚀 Future Enhancements

Potential features for future versions:
- [ ] iCloud sync across devices
- [ ] Recurring reminders
- [ ] Location-based reminders
- [ ] Siri integration
- [ ] Apple Watch companion app
- [ ] Widgets for Notification Center
- [ ] Time tracking for completed tasks
- [ ] Collaboration features

## 📄 License

This project is open source and available under the MIT License. Feel free to modify and distribute according to your needs.

---

**Enjoy your native macOS Reminders app! 🎉**

Built with ❤️ using SwiftUI and modern macOS technologies.
