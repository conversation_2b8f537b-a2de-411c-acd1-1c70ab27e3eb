/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Light theme colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --bg-tertiary: #e9ecef;
    --text-primary: #1d1d1f;
    --text-secondary: #6e6e73;
    --text-muted: #86868b;
    --accent-blue: #007aff;
    --accent-green: #34c759;
    --accent-red: #ff3b30;
    --accent-orange: #ff9500;
    --border-light: #d2d2d7;
    --shadow: rgba(0, 0, 0, 0.1);
    --shadow-hover: rgba(0, 0, 0, 0.15);
}

[data-theme="dark"] {
    /* Dark theme colors */
    --bg-primary: #1c1c1e;
    --bg-secondary: #2c2c2e;
    --bg-tertiary: #3a3a3c;
    --text-primary: #ffffff;
    --text-secondary: #ebebf5;
    --text-muted: #8e8e93;
    --border-light: #38383a;
    --shadow: rgba(0, 0, 0, 0.3);
    --shadow-hover: rgba(0, 0, 0, 0.4);
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Segoe UI', Roboto, sans-serif;
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    line-height: 1.6;
    transition: background-color 0.3s ease, color 0.3s ease;
}

.app-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
}

/* Header */
.app-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 20px 0;
}

.app-title {
    font-size: 2.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

.header-controls {
    display: flex;
    gap: 10px;
    align-items: center;
}

.sort-controls {
    margin-right: 10px;
}

.sort-select {
    padding: 8px 12px;
    border: 1px solid var(--border-light);
    border-radius: 6px;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 0.9rem;
    cursor: pointer;
}

.theme-toggle, .search-toggle {
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: 8px;
    padding: 8px 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 1.2rem;
}

.theme-toggle:hover, .search-toggle:hover {
    background: var(--bg-tertiary);
    transform: translateY(-1px);
}

.menu-toggle {
    position: relative;
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: 8px;
    padding: 8px 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 1.2rem;
}

.menu-toggle:hover {
    background: var(--bg-tertiary);
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: 8px;
    box-shadow: 0 4px 15px var(--shadow);
    padding: 8px 0;
    min-width: 160px;
    display: none;
    z-index: 100;
}

.menu-toggle:hover .dropdown-menu {
    display: block;
}

.dropdown-menu button,
.dropdown-menu .import-btn {
    width: 100%;
    padding: 8px 16px;
    border: none;
    background: none;
    color: var(--text-primary);
    text-align: left;
    cursor: pointer;
    font-size: 0.9rem;
    display: block;
    text-decoration: none;
}

.dropdown-menu button:hover,
.dropdown-menu .import-btn:hover {
    background: var(--bg-tertiary);
}

/* Search Container */
.search-container {
    background: var(--bg-primary);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px var(--shadow);
    display: none;
    animation: slideDown 0.3s ease;
}

.search-container.active {
    display: block;
}

.search-input {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid var(--border-light);
    border-radius: 8px;
    font-size: 1rem;
    background: var(--bg-secondary);
    color: var(--text-primary);
    margin-bottom: 15px;
}

.filter-controls {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.filter-select {
    padding: 8px 12px;
    border: 1px solid var(--border-light);
    border-radius: 6px;
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 0.9rem;
}

/* Add Reminder Form */
.add-reminder-container {
    background: var(--bg-primary);
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 2px 10px var(--shadow);
}

.form-row {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
    align-items: center;
}

.form-row:last-child {
    margin-bottom: 0;
}

.reminder-input {
    flex: 1;
    padding: 12px 16px;
    border: 1px solid var(--border-light);
    border-radius: 8px;
    font-size: 1rem;
    background: var(--bg-secondary);
    color: var(--text-primary);
    transition: border-color 0.2s ease;
}

.reminder-input:focus {
    outline: none;
    border-color: var(--accent-blue);
}

.add-button {
    background: var(--accent-blue);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.add-button:hover {
    background: #0056cc;
    transform: translateY(-1px);
}

.form-details {
    flex-wrap: wrap;
}

.date-input, .category-select, .priority-select {
    padding: 8px 12px;
    border: 1px solid var(--border-light);
    border-radius: 6px;
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 0.9rem;
}

/* Reminders List */
.reminders-container {
    min-height: 400px;
}

.reminder-item {
    background: var(--bg-primary);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 15px;
    box-shadow: 0 2px 8px var(--shadow);
    transition: all 0.2s ease;
    border-left: 4px solid var(--accent-blue);
}

.reminder-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px var(--shadow-hover);
}

.reminder-item.completed {
    opacity: 0.7;
    border-left-color: var(--accent-green);
}

.reminder-item.high-priority {
    border-left-color: var(--accent-red);
}

.reminder-item.medium-priority {
    border-left-color: var(--accent-orange);
}

.reminder-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 10px;
}

.reminder-text {
    font-size: 1.1rem;
    font-weight: 500;
    color: var(--text-primary);
    flex: 1;
    margin-right: 15px;
}

.reminder-text.completed {
    text-decoration: line-through;
    color: var(--text-muted);
}

.reminder-actions {
    display: flex;
    gap: 8px;
}

.action-btn {
    background: none;
    border: none;
    padding: 6px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 1rem;
    transition: background-color 0.2s ease;
}

.action-btn:hover {
    background: var(--bg-tertiary);
}

.reminder-meta {
    display: flex;
    gap: 15px;
    align-items: center;
    font-size: 0.85rem;
    color: var(--text-secondary);
}

.reminder-category {
    background: var(--bg-tertiary);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
}

.reminder-date {
    color: var(--text-muted);
}

.reminder-date.overdue {
    color: var(--accent-red);
    font-weight: 500;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: var(--text-muted);
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 20px;
}

.empty-state h3 {
    font-size: 1.5rem;
    margin-bottom: 10px;
    color: var(--text-secondary);
}

/* Footer */
.app-footer {
    margin-top: 40px;
    padding: 20px 0;
    border-top: 1px solid var(--border-light);
}

.stats {
    display: flex;
    justify-content: center;
    gap: 30px;
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-bottom: 15px;
}

.keyboard-shortcuts {
    text-align: center;
    color: var(--text-muted);
    font-size: 0.8rem;
}

/* Modal */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-overlay.active {
    display: flex;
}

.modal {
    background: var(--bg-primary);
    border-radius: 12px;
    padding: 30px;
    max-width: 500px;
    width: 90%;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-muted);
}

.modal-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 20px;
}

.cancel-button, .save-button {
    padding: 10px 20px;
    border-radius: 8px;
    border: none;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.2s ease;
}

.cancel-button {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.save-button {
    background: var(--accent-blue);
    color: white;
}

.cancel-button:hover {
    background: var(--border-light);
}

.save-button:hover {
    background: #0056cc;
}

/* Animations */
@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.reminder-item {
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .app-container {
        padding: 15px;
    }
    
    .app-title {
        font-size: 2rem;
    }
    
    .form-row {
        flex-direction: column;
        align-items: stretch;
    }
    
    .filter-controls {
        flex-direction: column;
    }
    
    .reminder-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .reminder-actions {
        margin-top: 10px;
    }
    
    .stats {
        flex-direction: column;
        gap: 10px;
    }
}
