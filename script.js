// Reminders App JavaScript
class RemindersApp {
    constructor() {
        this.reminders = this.loadReminders();
        this.currentEditId = null;
        this.init();
    }

    init() {
        this.bindEvents();
        this.renderReminders();
        this.updateStats();
        this.initTheme();
    }

    bindEvents() {
        // Form submission
        document.getElementById('addReminderForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.addReminder();
        });

        // Search and filters
        document.getElementById('searchToggle').addEventListener('click', () => {
            this.toggleSearch();
        });

        document.getElementById('searchInput').addEventListener('input', (e) => {
            this.filterReminders();
        });

        document.getElementById('categoryFilter').addEventListener('change', () => {
            this.filterReminders();
        });

        document.getElementById('statusFilter').addEventListener('change', () => {
            this.filterReminders();
        });

        // Theme toggle
        document.getElementById('themeToggle').addEventListener('click', () => {
            this.toggleTheme();
        });

        // Modal events
        document.getElementById('closeModal').addEventListener('click', () => {
            this.closeEditModal();
        });

        document.getElementById('cancelEdit').addEventListener('click', () => {
            this.closeEditModal();
        });

        document.getElementById('editReminderForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveEdit();
        });

        // Click outside modal to close
        document.getElementById('editModal').addEventListener('click', (e) => {
            if (e.target.classList.contains('modal-overlay')) {
                this.closeEditModal();
            }
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.metaKey || e.ctrlKey) {
                switch(e.key) {
                    case 'k':
                        e.preventDefault();
                        this.toggleSearch();
                        break;
                    case 'n':
                        e.preventDefault();
                        document.getElementById('reminderText').focus();
                        break;
                    case 'a':
                        e.preventDefault();
                        this.selectAllReminders();
                        break;
                    case 'd':
                        e.preventDefault();
                        this.deleteSelectedReminders();
                        break;
                }
            }
            if (e.key === 'Escape') {
                this.closeEditModal();
                this.hideSearch();
            }
        });

        // Auto-save as user types
        document.getElementById('reminderText').addEventListener('input', () => {
            this.saveDraft();
        });

        // Load draft on focus
        document.getElementById('reminderText').addEventListener('focus', () => {
            this.loadDraft();
        });

        // Sort functionality
        document.getElementById('sortSelect').addEventListener('change', (e) => {
            this.sortReminders(e.target.value);
        });
    }

    addReminder() {
        const text = document.getElementById('reminderText').value.trim();
        const date = document.getElementById('reminderDate').value;
        const category = document.getElementById('reminderCategory').value;
        const priority = document.getElementById('reminderPriority').value;

        if (!text) return;

        const reminder = {
            id: Date.now().toString(),
            text,
            date,
            category,
            priority,
            completed: false,
            createdAt: new Date().toISOString()
        };

        this.reminders.unshift(reminder);
        this.saveReminders();
        this.renderReminders();
        this.updateStats();
        this.clearForm();
        this.clearDraft();
    }

    clearForm() {
        document.getElementById('reminderText').value = '';
        document.getElementById('reminderDate').value = '';
        document.getElementById('reminderCategory').value = 'personal';
        document.getElementById('reminderPriority').value = 'medium';
    }

    toggleComplete(id) {
        const reminder = this.reminders.find(r => r.id === id);
        if (reminder) {
            reminder.completed = !reminder.completed;
            this.saveReminders();
            this.renderReminders();
            this.updateStats();
        }
    }

    deleteReminder(id) {
        if (confirm('Are you sure you want to delete this reminder?')) {
            this.reminders = this.reminders.filter(r => r.id !== id);
            this.saveReminders();
            this.renderReminders();
            this.updateStats();
        }
    }

    editReminder(id) {
        const reminder = this.reminders.find(r => r.id === id);
        if (reminder) {
            this.currentEditId = id;
            document.getElementById('editReminderText').value = reminder.text;
            document.getElementById('editReminderDate').value = reminder.date;
            document.getElementById('editReminderCategory').value = reminder.category;
            document.getElementById('editReminderPriority').value = reminder.priority;
            document.getElementById('editModal').classList.add('active');
        }
    }

    saveEdit() {
        const reminder = this.reminders.find(r => r.id === this.currentEditId);
        if (reminder) {
            reminder.text = document.getElementById('editReminderText').value.trim();
            reminder.date = document.getElementById('editReminderDate').value;
            reminder.category = document.getElementById('editReminderCategory').value;
            reminder.priority = document.getElementById('editReminderPriority').value;
            
            this.saveReminders();
            this.renderReminders();
            this.updateStats();
            this.closeEditModal();
        }
    }

    closeEditModal() {
        document.getElementById('editModal').classList.remove('active');
        this.currentEditId = null;
    }

    toggleSearch() {
        const searchContainer = document.getElementById('searchContainer');
        const searchInput = document.getElementById('searchInput');
        
        if (searchContainer.classList.contains('active')) {
            this.hideSearch();
        } else {
            searchContainer.classList.add('active');
            searchInput.focus();
        }
    }

    hideSearch() {
        const searchContainer = document.getElementById('searchContainer');
        searchContainer.classList.remove('active');
        document.getElementById('searchInput').value = '';
        document.getElementById('categoryFilter').value = '';
        document.getElementById('statusFilter').value = '';
        this.renderReminders();
    }

    filterReminders() {
        const searchTerm = document.getElementById('searchInput').value.toLowerCase();
        const categoryFilter = document.getElementById('categoryFilter').value;
        const statusFilter = document.getElementById('statusFilter').value;

        let filtered = this.reminders.filter(reminder => {
            const matchesSearch = reminder.text.toLowerCase().includes(searchTerm);
            const matchesCategory = !categoryFilter || reminder.category === categoryFilter;
            const matchesStatus = !statusFilter || 
                (statusFilter === 'completed' && reminder.completed) ||
                (statusFilter === 'pending' && !reminder.completed);

            return matchesSearch && matchesCategory && matchesStatus;
        });

        this.renderReminders(filtered);
    }

    renderReminders(remindersToRender = this.reminders) {
        const container = document.getElementById('remindersList');
        const emptyState = document.getElementById('emptyState');

        if (remindersToRender.length === 0) {
            container.innerHTML = '';
            emptyState.style.display = 'block';
            return;
        }

        emptyState.style.display = 'none';
        
        container.innerHTML = remindersToRender.map(reminder => {
            const isOverdue = reminder.date && new Date(reminder.date) < new Date() && !reminder.completed;
            const priorityClass = reminder.priority === 'high' ? 'high-priority' : 
                                 reminder.priority === 'medium' ? 'medium-priority' : '';
            
            return `
                <div class="reminder-item ${reminder.completed ? 'completed' : ''} ${priorityClass}">
                    <div class="reminder-header">
                        <div class="reminder-text ${reminder.completed ? 'completed' : ''}">${reminder.text}</div>
                        <div class="reminder-actions">
                            <button class="action-btn" onclick="app.toggleComplete('${reminder.id}')" title="${reminder.completed ? 'Mark as pending' : 'Mark as complete'}">
                                ${reminder.completed ? '↩️' : '✅'}
                            </button>
                            <button class="action-btn" onclick="app.editReminder('${reminder.id}')" title="Edit reminder">
                                ✏️
                            </button>
                            <button class="action-btn" onclick="app.deleteReminder('${reminder.id}')" title="Delete reminder">
                                🗑️
                            </button>
                        </div>
                    </div>
                    <div class="reminder-meta">
                        <span class="reminder-category">${reminder.category}</span>
                        ${reminder.date ? `<span class="reminder-date ${isOverdue ? 'overdue' : ''}">${this.formatDate(reminder.date)}</span>` : ''}
                        <span class="reminder-priority">Priority: ${reminder.priority}</span>
                    </div>
                </div>
            `;
        }).join('');
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        const today = new Date();
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);

        if (date.toDateString() === today.toDateString()) {
            return 'Today';
        } else if (date.toDateString() === tomorrow.toDateString()) {
            return 'Tomorrow';
        } else {
            return date.toLocaleDateString();
        }
    }

    updateStats() {
        const total = this.reminders.length;
        const completed = this.reminders.filter(r => r.completed).length;
        
        document.getElementById('totalCount').textContent = `${total} reminder${total !== 1 ? 's' : ''}`;
        document.getElementById('completedCount').textContent = `${completed} completed`;
    }

    toggleTheme() {
        const currentTheme = document.documentElement.getAttribute('data-theme');
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
        
        document.documentElement.setAttribute('data-theme', newTheme);
        localStorage.setItem('theme', newTheme);
        
        const themeIcon = document.querySelector('.theme-icon');
        themeIcon.textContent = newTheme === 'dark' ? '☀️' : '🌙';
    }

    initTheme() {
        const savedTheme = localStorage.getItem('theme') || 'light';
        document.documentElement.setAttribute('data-theme', savedTheme);
        
        const themeIcon = document.querySelector('.theme-icon');
        themeIcon.textContent = savedTheme === 'dark' ? '☀️' : '🌙';
    }

    saveReminders() {
        localStorage.setItem('reminders', JSON.stringify(this.reminders));
    }

    loadReminders() {
        const saved = localStorage.getItem('reminders');
        return saved ? JSON.parse(saved) : [];
    }

    // Advanced features
    saveDraft() {
        const text = document.getElementById('reminderText').value;
        if (text.trim()) {
            localStorage.setItem('reminderDraft', text);
        }
    }

    loadDraft() {
        const draft = localStorage.getItem('reminderDraft');
        if (draft && !document.getElementById('reminderText').value) {
            document.getElementById('reminderText').value = draft;
        }
    }

    clearDraft() {
        localStorage.removeItem('reminderDraft');
    }

    selectAllReminders() {
        // Toggle selection of all visible reminders
        const checkboxes = document.querySelectorAll('.reminder-checkbox');
        const allChecked = Array.from(checkboxes).every(cb => cb.checked);
        checkboxes.forEach(cb => cb.checked = !allChecked);
    }

    deleteSelectedReminders() {
        const checkboxes = document.querySelectorAll('.reminder-checkbox:checked');
        if (checkboxes.length === 0) return;

        if (confirm(`Delete ${checkboxes.length} selected reminder(s)?`)) {
            const idsToDelete = Array.from(checkboxes).map(cb => cb.dataset.id);
            this.reminders = this.reminders.filter(r => !idsToDelete.includes(r.id));
            this.saveReminders();
            this.renderReminders();
            this.updateStats();
        }
    }

    exportReminders() {
        const dataStr = JSON.stringify(this.reminders, null, 2);
        const dataBlob = new Blob([dataStr], {type: 'application/json'});
        const url = URL.createObjectURL(dataBlob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `reminders-${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        URL.revokeObjectURL(url);
    }

    importReminders(event) {
        const file = event.target.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const importedReminders = JSON.parse(e.target.result);
                if (confirm('This will replace all current reminders. Continue?')) {
                    this.reminders = importedReminders;
                    this.saveReminders();
                    this.renderReminders();
                    this.updateStats();
                }
            } catch (error) {
                alert('Invalid file format');
            }
        };
        reader.readAsText(file);
    }

    // Sort reminders by different criteria
    sortReminders(criteria) {
        switch(criteria) {
            case 'date':
                this.reminders.sort((a, b) => {
                    if (!a.date && !b.date) return 0;
                    if (!a.date) return 1;
                    if (!b.date) return -1;
                    return new Date(a.date) - new Date(b.date);
                });
                break;
            case 'priority':
                const priorityOrder = { high: 3, medium: 2, low: 1 };
                this.reminders.sort((a, b) => priorityOrder[b.priority] - priorityOrder[a.priority]);
                break;
            case 'category':
                this.reminders.sort((a, b) => a.category.localeCompare(b.category));
                break;
            case 'created':
                this.reminders.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
                break;
        }
        this.renderReminders();
    }
}

// Initialize the app
const app = new RemindersApp();
